<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="960px"
    :before-close="handleClose"
    class="add-unit-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <el-form
          ref="addUnitForm"
          :model="formData"
          :rules="formRules"
        
        >
          <div class="form-row">
            
              <el-form-item :label="$T('机组名称')" prop="unitName">
                <el-input
                  v-model="formData.unitName"
                  :placeholder="$T('请输入内容')"
                  class="form-input"
                />
              </el-form-item>
              <el-form-item :label="$T('机组类型')" prop="unitType">
                <el-select
                  v-model="formData.unitType"
                  class="form-select"
                  :placeholder="$T('请选择机组类型')"
                  :disabled="mode === 'edit'"
                >
                  <el-option
                    v-for="option in unitTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
           
          </div>
        </el-form>
      </div>

      <!-- 资源列表区域 -->
      <div v-if="formData.unitType" class="resource-section">
        <div class="section-title">{{ resourceListTitle }}</div>
        
        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <div class="filter-left">
            <el-input
              v-model="resourceSearch"
              :placeholder="$T('请输入关键字')"
              prefix-icon="el-icon-search"
              class="search-input"
            />
            <CustomElSelect
              v-model="selectedArea"
              :prefix_in="$T('区域')"
              class="area-select"
            >
              <el-option
                v-for="option in areaOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </CustomElSelect>
          </div>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <el-table
            ref="resourceTable"
            :data="currentResourceData"
            @selection-change="handleSelectionChange"
            max-height="440"
            v-loading="resourceLoading"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            />
            <el-table-column
              prop="index"
              :label="$T('序号')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ String(scope.row.index).padStart(2, '0') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="resource_id"
              :label="$T('资源ID')"
              min-width="180"
            />
            <el-table-column
              prop="resource_name"
              :label="$T('资源名称')"
              min-width="150"
            />
            <el-table-column
              prop="area"
              :label="$T('区域')"
              min-width="100"
            />
            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="140"
            />
            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T('是') : $T('否') }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="resource-pagination">
          <div class="pagination-controls">
            <div class="pagination-info">
              <span class="total-count">
                {{ $T('共') }}<span class="count-number">{{ resourceTotalCount }}</span>{{ $T('个') }}
              </span>
            </div>

            <div class="page-size-selector">
              <el-select v-model="resourcePageSize" class="page-size-select" @change="handlePageSizeChange">
                <el-option
                  v-for="size in pageSizeOptions"
                  :key="size"
                  :label="`${size}${$T('条/页')}`"
                  :value="size"
                />
              </el-select>
            </div>

            <el-pagination
              :current-page="resourceCurrentPage"
              :page-size="resourcePageSize"
              :total="resourceTotalCount"
              layout="prev, pager, next, jumper"
              @current-change="handleResourcePageChange"
              class="pagination-component"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ $T('取消') }}</el-button>
      <el-button type="primary" @click="handleConfirm">{{ $T('确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAvailableResources, getDistricts, createUnit, updateUnit } from '@/api/resource-aggregation';

export default {
  name: 'AddUnitDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add',
      validator: value => ['add', 'edit'].includes(value)
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      
      // 表单数据
      formData: {
        unitName: '',
        unitType: ''
      },

      // 表单验证规则
      formRules: {
        unitName: [
          { required: true, message: this.$T('请输入机组名称'), trigger: 'blur' },
          { min: 1, max: 50, message: this.$T('长度在 1 到 50 个字符'), trigger: 'blur' }
        ],
        unitType: [
          { required: true, message: this.$T('请选择机组类型'), trigger: 'change' }
        ]
      },
      
      // 机组类型选项
      unitTypeOptions: [
        { label: this.$T('调峰机组'), value: 'peak_shaving' },
        { label: this.$T('调频机组'), value: 'frequency_regulation' },
        { label: this.$T('需求响应机组'), value: 'demand_response' },
      ],
      
      // 资源搜索和筛选
      resourceSearch: '',
      selectedArea: 'all',
      areaOptions: [
        { label: this.$T('全部'), value: 'all' },
        { label: '华北区域', value: 'north' },
        { label: '华东区域', value: 'east' },
        { label: '华南区域', value: 'south' },
        { label: '西北区域', value: 'northwest' }
      ],
      
      // 资源列表数据
      allResourceData: [],
      selectedResources: [],
      resourceLoading: false,

      // 分页
      resourceCurrentPage: 1,
      resourcePageSize: 10,
      pageSizeOptions: [10, 20, 50, 100]
    };
  },
  computed: {
    // 弹窗标题
    dialogTitle() {
      return this.mode === 'edit' ? this.$T('编辑') : this.$T('新增');
    },

    // 资源列表标题
    resourceListTitle() {
      if (this.formData.unitType) {
        const selectedOption = this.unitTypeOptions.find(option => option.value === this.formData.unitType);
        const listType = this.mode === 'edit' ? this.$T('剩余资源列表') : this.$T('资源列表');
        return selectedOption ? `${selectedOption.label}${listType}` : `${this.$T('机组')}${listType}`;
      }
      return this.mode === 'edit' ? this.$T('机组剩余资源列表') : this.$T('机组资源列表');
    },

    // 当前页资源数据 - 现在直接使用API返回的数据
    currentResourceData() {
      return this.allResourceData;
    },

    resourceTotalCount() {
      return this.allResourceData.length;
    }
  },
  watch: {
    visible: {
      handler(val) {
        console.log('AddUnitDialog visible prop changed:', val);
        this.dialogVisible = val;
        if (val) {
          this.initDialog();
        }
      },
      immediate: true
    },

    mode: {
      handler() {
        if (this.dialogVisible) {
          this.initDialog();
        }
      }
    },

    editData: {
      handler() {
        if (this.dialogVisible && this.mode === 'edit') {
          this.initDialog();
        }
      },
      deep: true
    },

    dialogVisible(val) {
      console.log('AddUnitDialog dialogVisible changed:', val);
      this.$emit('update:visible', val);
    },

    // 监听搜索关键字变化
    resourceSearch() {
      this.resourceCurrentPage = 1;
      this.loadAvailableResources();
    },

    // 监听区域筛选变化
    selectedArea() {
      this.resourceCurrentPage = 1;
      this.loadAvailableResources();
    },

    // 监听机组类型变化，显示提示信息并加载资源
    'formData.unitType'(newType, oldType) {
      if (newType && newType !== oldType) {
        this.showUnitTypeMessage(newType);
        this.selectedArea = 'all';
        this.allResourceData = [];
        this.selectedResources = [];
      }
    }
  },
  created() {
    console.log('AddUnitDialog created');
    this.loadDistricts();
  },
  mounted() {
    console.log('AddUnitDialog mounted, visible prop:', this.visible);
  },
  methods: {
    // 加载区域数据
    async loadDistricts() {
      try {
        const response = await getDistricts();
        if (response && response.data && response.data.list) {
          const districts = response.data.list.map(item => ({
            label: item.district,
            value: item.district
          }));
          this.areaOptions = [
            { label: this.$T('全部'), value: 'all' },
            ...districts
          ];
        }
      } catch (error) {
        console.error('加载区域数据失败:', error);
        // 使用默认区域数据
      }
    },

    // 加载可用资源
    async loadAvailableResources() {
      if (!this.formData.unitType || !this.selectedArea || this.selectedArea === 'all') {
        return;
      }

      try {
        this.resourceLoading = true;
        const params = {
          unit_type: this.formData.unitType,
          district: this.selectedArea
        };

        // 编辑模式下传入unit_id
        if (this.mode === 'edit' && this.editData && this.editData.unit_id) {
          params.unit_id = this.editData.unit_id;
        }

        const response = await getAvailableResources(params);
        if (response && response.data && response.data.list) {
          this.allResourceData = response.data.list.map((item, index) => ({
            index: index + 1,
            resource_id: item.resource_id,
            resource_name: item.resource_name,
            // 这里可能需要根据实际API返回的字段调整
            area: this.selectedArea,
            capacity: Math.floor(Math.random() * 1000) + 100, // 临时mock数据
            directControl: Math.random() > 0.5 // 临时mock数据
          }));
        }
      } catch (error) {
        console.error('加载可用资源失败:', error);
        this.$message.error(this.$T('加载资源数据失败'));
        // 使用mock数据作为fallback
        this.generateResourceMockData();
      } finally {
        this.resourceLoading = false;
      }
    },

    // 初始化弹窗
    initDialog() {
      if (this.mode === 'edit' && this.editData) {
        // 编辑模式：使用传入的数据
        this.formData = {
          unitName: this.editData.unitName || '',
          unitType: this.editData.unitType || ''
        };
        this.selectedResources = [];
      } else {
        // 新增模式：使用空数据
        this.formData = {
          unitName: '',
          unitType: ''
        };
        this.selectedResources = [];
      }

      this.resourceSearch = '';
      this.selectedArea = 'all';
      this.resourceCurrentPage = 1;
      this.allResourceData = [];

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.addUnitForm) {
          this.$refs.addUnitForm.clearValidate();
        }
      });
    },



    // 显示机组类型提示信息
    showUnitTypeMessage(unitType) {
      let message = '';
      switch (unitType) {
        case 'frequency_regulation':
          message = this.$T('调频机组只能绑定直控型的资源，一般以同一个并网节点聚合');
          break;
        case 'peak_shaving':
          message = this.$T('调峰机组一般以同一个并网节点聚合');
          break;
        case 'demand_response':
          message = this.$T('需求响应机组一般以资源所在地同一个地市聚合');
          break;
        default:
          return;
      }

      if (message) {
        this.$message({
          message: message,
          type: 'info',
          showClose: true
        });
      }
    },
    
    // 生成资源Mock数据（作为API调用失败时的fallback）
    generateResourceMockData() {
      const data = [];
      const areaConfigs = [
        { label: '华北区域', value: 'north' },
        { label: '华东区域', value: 'east' },
        { label: '华南区域', value: 'south' },
        { label: '西北区域', value: 'northwest' }
      ];

      for (let i = 1; i <= 90; i++) {
        const areaConfig = areaConfigs[Math.floor(Math.random() * areaConfigs.length)];
        data.push({
          index: i,
          resource_id: `9144030078525478X${i}`,
          resource_name: `资源${i}`,
          area: areaConfig.label,
          capacity: Math.floor(Math.random() * 1000) + 100, // 100-1100 kVA
          directControl: Math.random() > 0.5 // 随机生成是否平台直控
        });
      }
      this.allResourceData = data;
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedResources = selection;
    },
    
    // 资源分页变化
    handleResourcePageChange(page) {
      this.resourceCurrentPage = page;
      this.loadAvailableResources();
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.resourcePageSize = size;
      this.resourceCurrentPage = 1;
      this.loadAvailableResources();
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
    },
    
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    
    // 确定
    handleConfirm() {
      // 表单验证
      this.$refs.addUnitForm.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = {
              unit_name: this.formData.unitName,
              unit_type: this.formData.unitType,
              resource_ids: this.selectedResources.map(item => item.resource_id)
            };

            if (this.mode === 'edit') {
              await updateUnit(this.editData.unit_id, submitData);
              this.$emit('update', submitData);
              this.$message.success(this.$T('编辑成功'));
            } else {
              await createUnit(submitData);
              this.$emit('confirm', submitData);
              this.$message.success(this.$T('新增成功'));
            }

            this.dialogVisible = false;
          } catch (error) {
            console.error('操作失败:', error);
            this.$message.error(this.$T('操作失败'));
          }
        } else {
          this.$message.warning(this.$T('请完善表单信息'));
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.add-unit-dialog {
  // 弹窗整体居中
  :deep(.el-dialog) {
    margin-top: 5vh !important;
  }

  // ::v-deep .el-dialog__wrapper {
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }
  .dialog-content {
    @include padding(J4);
    
    .form-section {
      .form-row {
        display: flex;
        gap: var(--J4);
      }
    }
    
    .resource-section {
      .section-title {
        @include font_size(Aa);
        @include font_color(T1);
        @include margin_bottom(J2);
      }
      
      .resource-filter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include margin_bottom(J3);
        
        .filter-left {
          display: flex;
          gap: var(--J3);
          
          .search-input {
            width: 240px;
          }
          
          .area-select {
            width: 240px;
          }
        }
        
        .bind-btn {
          opacity: 0;
        }
      }
      
      .resource-table {
        @include margin_bottom(J3);
      }
      
      .resource-pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .pagination-controls {
          display: flex;
          align-items: center;
          gap: var(--J3);

          .pagination-info {
            .total-count {
              @include font_color(T2);
              @include font_size(Aa);

              .count-number {
                @include font_color(ZS);
              }
            }
          }

          .page-size-selector {
            .page-size-select {
              width: 100px;
            }
          }
        }
      }
    }
  }
}
</style>
