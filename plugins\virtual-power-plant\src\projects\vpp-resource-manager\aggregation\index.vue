<template>
  <div class="aggregation-container">
    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <div class="search-filter-container">
        <!-- 搜索框和筛选器 -->
        <div class="search-filter-left">
          <!-- 搜索框 -->
          <div class="search-input-wrapper">
            <el-input
              v-model="searchKeyword"
              :placeholder="$T('请输入关键字')"
              prefix-icon="el-icon-search"
              class="search-input"
            />
          </div>

          <!-- 机组类型筛选 -->
          <div class="filter-select-wrapper">
            <CustomElSelect
              v-model="selectedType"
              :prefix_in="$T('机组类型')"
              class="filter-select"
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </CustomElSelect>
          </div>
        </div>

        <!-- 新增按钮 -->
        <div class="action-buttons">
          <el-button type="primary" @click="handleAdd">
            {{ $T('新增') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        :data="currentPageData"
        class="data-table"
        max-height="640"
        v-loading="loading"
      >
        <el-table-column
          prop="unit_id"
          :label="$T('序号')"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            {{ String(scope.$index + 1 + (currentPage - 1) * pageSize).padStart(2, '0') }}
          </template>
        </el-table-column>

        <el-table-column
          prop="unit_name"
          :label="$T('机组名称')"
          min-width="200"
        />

        <el-table-column
          prop="unit_type"
          :label="$T('机组类型')"
          min-width="150"
        >
          <template slot-scope="scope">
            {{ getUnitTypeLabel(scope.row.unit_type) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="resource_count"
          :label="$T('聚合资源数量')"
          min-width="120"

        />

        <el-table-column
          :label="$T('操作')"
          width="200"
          
        >
          <template slot-scope="scope">
            <div class="action-buttons-cell">
              <el-button
                type="text"
                class="action-btn detail-btn"
                @click="handleDetail(scope.row)"
              >
                {{ $T('详情') }}
              </el-button>
              <el-button
                type="text"
                class="action-btn edit-btn"
                @click="handleEdit(scope.row)"
              >
                {{ $T('编辑') }}
              </el-button>
              <el-button
                type="text"
                class="action-btn delete-btn"
                @click="handleDelete(scope.row)"
              >
                {{ $T('删除') }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <div class="pagination-controls">
        <div class="pagination-info">
          <span class="total-count">
            {{ $T('共') }}<span class="count-number">{{ totalCount }}</span>{{ $T('个') }}
          </span>
        </div>

        <div class="page-size-selector">
          <el-select v-model="pageSize" class="page-size-select">
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size}${$T('条/页')}`"
              :value="size"
            />
          </el-select>
        </div>

        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          class="pagination-component"
        />
      </div>
    </div>
    <!-- 新增弹窗 -->
    <AddUnitDialog
      :visible.sync="addDialogVisible"
      mode="add"
      @confirm="handleAddConfirm"
    />

    <!-- 编辑弹窗 -->
    <AddUnitDialog
      :visible.sync="editDialogVisible"
      mode="edit"
      :editData="currentEditData"
      @update="handleEditConfirm"
    />

    <!-- 详情抽屉 -->
    <DetailDrawer
      :visible.sync="detailDrawerVisible"
      :detail-data="currentDetailData"
    />
  </div>

</template>

<script>
import AddUnitDialog from './components/AddUnitDialog.vue';
import DetailDrawer from './components/DetailDrawer.vue';
import { listUnits, deleteUnit } from '@/api/resource-aggregation';

export default {
  name: 'VppResourceManager',
  components: {
    AddUnitDialog,
    DetailDrawer
  },
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedType: 'all',
      typeOptions: [
        { label: this.$T('全部'), value: 'all' },
        { label: this.$T('调峰机组'), value: 'peak_shaving' },
        { label: this.$T('调频机组'), value: 'frequency_regulation' },
        { label: this.$T('需求响应机组'), value: 'demand_response' },
      ],

      // 表格数据
      allTableData: [],
      loading: false,
      total: 0,

      // 分页
      currentPage: 1,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100],

      // 新增弹窗
      addDialogVisible: false,

      // 编辑弹窗
      editDialogVisible: false,
      currentEditData: {},

      // 详情抽屉
      detailDrawerVisible: false,
      currentDetailData: {}
    };
  },
  computed: {
    // 当前页数据 - 现在直接使用API返回的数据
    currentPageData() {
      return this.allTableData;
    },

    // 总数
    totalCount() {
      return this.total;
    }
  },
  created() {
    this.loadUnits();
  },
  methods: {
    // 加载机组列表
    async loadUnits() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加机组类型筛选
        if (this.selectedType && this.selectedType !== 'all') {
          params.unit_type = this.selectedType;
        }

        const response = await listUnits(params);
        if (response && response.data) {
          this.allTableData = response.data.list || [];
          this.total = response.data.total || 0;
        }
      } catch (error) {
        console.error('加载机组列表失败:', error);
        this.$message.error(this.$T('加载数据失败'));
        // 使用mock数据作为fallback
        this.generateMockData();
      } finally {
        this.loading = false;
      }
    },

    // 生成Mock数据（作为API调用失败时的fallback）
    generateMockData() {
      const data = [];
      const unitTypes = [
        { label: '调峰机组', value: 'peak_shaving' },
        { label: '调频机组', value: 'frequency_regulation' },
        { label: '需求响应机组', value: 'demand_response' }
      ];
      const unitNames = [
        '1#调峰机组',
        '2#调峰机组',
        '3#调峰机组',
        '1#调频机组',
        '2#调频机组',
        '1#需求响应机组',
        '2#需求响应机组'
      ];

      // 生成1522条数据以匹配总数显示
      for (let i = 1; i <= 1522; i++) {
        const unitType = unitTypes[Math.floor(Math.random() * unitTypes.length)];
        data.push({
          unit_id: i.toString(),
          unit_name: unitNames[Math.floor(Math.random() * unitNames.length)],
          unit_type: unitType.value,
          resource_count: Math.floor(Math.random() * 50) + 10, // 10-59之间的随机数
          user_id: 'user001'
        });
      }
      this.allTableData = data;
      this.total = 1522;
    },

    // 新增
    handleAdd() {
      console.log('点击新增按钮，准备打开弹窗');
      this.addDialogVisible = true;
      console.log('addDialogVisible 设置为:', this.addDialogVisible);
    },

    // 新增确认
    handleAddConfirm(data) {
      console.log('新增机组数据:', data);
      // 重新加载列表数据
      this.loadUnits();
    },

    // 获取机组类型标签
    getUnitTypeLabel(value) {
      const option = this.typeOptions.find(opt => opt.value === value);
      return option ? option.label : value;
    },

    // 详情
    handleDetail(row) {
      console.log('查看详情:', row);
      this.currentDetailData = { ...row };
      this.detailDrawerVisible = true;
    },

    // 编辑
    handleEdit(row) {
      console.log('编辑机组:', row);
      // 设置编辑数据
      this.currentEditData = {
        unit_id: row.unit_id,
        unitName: row.unit_name,
        unitType: row.unit_type
      };
      this.editDialogVisible = true;
    },

    // 编辑确认
    handleEditConfirm(data) {
      console.log('编辑机组数据:', data);
      // 重新加载列表数据
      this.loadUnits();
    },

    // 删除
    handleDelete(row) {
      console.log('删除机组:', row);
      this.$confirm(
        this.$T('确定要删除该机组吗？删除后，其与资源的绑定关系也一并解除。'),
        this.$T('提示'),
        {
          confirmButtonText: this.$T('确定'),
          cancelButtonText: this.$T('取消'),
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteUnit(row.unit_id);
          this.$message.success(this.$T('删除成功'));
          // 重新加载数据
          this.loadUnits();
        } catch (error) {
          console.error('删除机组失败:', error);
          this.$message.error(this.$T('删除失败'));
        }
      }).catch(() => {
        // 取消删除
      });
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadUnits();
    }
  },

  watch: {
    // 监听筛选条件变化
    selectedType() {
      this.currentPage = 1;
      this.loadUnits();
    },

    // 监听搜索关键字变化
    searchKeyword() {
      this.currentPage = 1;
      // 这里可以添加防抖处理
      this.loadUnits();
    }
  }
};
</script>

<style lang="scss" scoped>
.aggregation-container {
  @include background_color(BG1);
  @include padding(J4);

  .search-filter-section {
    border-radius: var(--Ra);
    @include margin_bottom(J3);

    .search-filter-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--J3);

      .search-filter-left {
        display: flex;
        align-items: center;
        gap: var(--J3);

        .search-input-wrapper {
          .search-input {
            width: 240px;
          }
        }

        .filter-select-wrapper {
          .filter-select {
            width: 240px;
          }
        }
      }
    }
  }

  .table-section {
    @include background_color(BG1);
    border-radius: var(--Ra);
    overflow: hidden;
    @include margin_bottom(J3);

    .data-table {
      width: 100%;

      .action-buttons-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--J4);

        .action-btn {
          @include font_size(Aa);
          padding: 0;

          &.detail-btn,
          &.edit-btn {
            @include font_color(ZS);
          }

          &.delete-btn {
            @include font_color(Sta3);
          }
        }
      }
    }
  }

  .pagination-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: var(--J3);

      .pagination-info {
        .total-count {
          @include font_color(T2);
          @include font_size(Aa);

          .count-number {
            @include font_color(ZS);
          }
        }
      }

      .page-size-selector {
        .page-size-select {
          width: 100px;
        }
      }
    }
  }
}
</style>